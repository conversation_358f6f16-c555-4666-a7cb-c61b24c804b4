# CIFAR-10 Classification Agent Requirements

# Core ML/DL frameworks
tensorflow>=2.13.0
keras>=2.13.0
numpy>=1.21.0
scipy>=1.7.0

# Data handling and visualization
matplotlib>=3.5.0
seaborn>=0.11.0
pandas>=1.3.0
pillow>=8.3.0

# Progress bars and utilities
tqdm>=4.62.0
click>=8.0.0

# Optional: For advanced features
scikit-learn>=1.0.0
opencv-python>=4.5.0

# Development and testing (optional)
pytest>=6.2.0
jupyter>=1.0.0
ipykernel>=6.0.0

# For mixed precision and GPU optimization
# These are included with TensorFlow 2.13+
# tensorrt>=8.0.0  # Uncomment if using NVIDIA TensorRT
# cuda-toolkit>=11.8  # Uncomment if manual CUDA setup needed
