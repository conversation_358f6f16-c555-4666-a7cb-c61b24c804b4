🚀 CIFAR-10 Agent Training Commands Reference
=====================================================

This file contains all the exciting training commands you can run with your CIFAR-10 agent,
complete with explanations and expected results!

🏗️ ARCHITECTURE EXPERIMENTS
============================

# Advanced model with residual connections (better accuracy)
python cifar10_agent.py train --architecture advanced --epochs 10

# Compare basic vs advanced on same epochs
python cifar10_agent.py train --architecture basic --epochs 25
python cifar10_agent.py train --architecture advanced --epochs 25

⚡ PERFORMANCE OPTIMIZATIONS
============================

# Mixed precision training (faster on modern GPUs)
python cifar10_agent.py train --architecture advanced --epochs 50 --mixed-precision

# Longer training with patience
python cifar10_agent.py train --architecture advanced --epochs 200 --patience 20

🎛️ HYPERPARAMETER TUNING
=========================

# Higher learning rate for faster convergence
python cifar10_agent.py train --architecture basic --epochs 30 --learning-rate 0.01

# Lower learning rate for fine-tuning
python cifar10_agent.py train --architecture advanced --epochs 100 --learning-rate 0.0001

# Different validation split (use 20% for validation instead of 10%)
python cifar10_agent.py train --architecture basic --epochs 50 --validation-split 0.2

🔧 ADVANCED TRAINING CONFIGURATIONS
===================================

# Aggressive early stopping
python cifar10_agent.py train --architecture advanced --epochs 100 --patience 5 --lr-factor 0.2

# Conservative training with minimal LR reduction
python cifar10_agent.py train --architecture basic --epochs 75 --patience 15 --lr-factor 0.8 --min-lr 1e-8

📊 EVALUATION & PREDICTION COMMANDS
===================================

# Evaluate your trained models
python cifar10_agent.py evaluate --model-path checkpoints/best_model_*.h5

# Compare different model checkpoints
ls checkpoints/
python cifar10_agent.py evaluate --model-path checkpoints/[specific_model].h5

# Single image prediction (download a test image first)
python cifar10_agent.py predict --image-path test_image.jpg --model-path checkpoints/best_model_*.h5

📋 DATASET INFORMATION COMMANDS
===============================

# Show all dataset info
python cifar10_agent.py info --show-classes --show-stats --show-sample

# Just show the 10 CIFAR-10 classes
python cifar10_agent.py info --show-classes

# Show sample images (saves cifar10_samples.png)
python cifar10_agent.py info --show-sample

🏆 RECOMMENDED TRAINING EXPERIMENTS
===================================

🥇 Quick Comparison (30 minutes):
---------------------------------
# Test both architectures quickly
python cifar10_agent.py train --architecture basic --epochs 20
python cifar10_agent.py train --architecture advanced --epochs 20

🥈 Serious Training (2-3 hours):
--------------------------------
# High-performance training
python cifar10_agent.py train --architecture advanced --epochs 100 --mixed-precision --patience 15

🥉 Hyperparameter Search:
-------------------------
# Try different learning rates
python cifar10_agent.py train --architecture basic --epochs 30 --learning-rate 0.01
python cifar10_agent.py train --architecture basic --epochs 30 --learning-rate 0.001  
python cifar10_agent.py train --architecture basic --epochs 30 --learning-rate 0.0001

📈 EXPECTED RESULTS
===================

Command                                          | Expected Accuracy | Training Time | Best For
------------------------------------------------|------------------|---------------|----------
basic --epochs 20                              | ~70-75%          | 20-30 min     | Quick testing
basic --epochs 50                              | ~75-80%          | 45-60 min     | Baseline
advanced --epochs 50                           | ~80-85%          | 1-2 hours     | Good balance
advanced --epochs 100 --mixed-precision        | ~85-90%          | 2-3 hours     | Best accuracy

🎮 FUN EXPERIMENTS
==================

🔬 Learning Rate Scheduling:
---------------------------
# The cosine annealing will kick in automatically after epoch 10
python cifar10_agent.py train --architecture advanced --epochs 60 --learning-rate 0.01

🎯 Patience Testing:
-------------------
# See how early stopping works
python cifar10_agent.py train --architecture basic --epochs 100 --patience 5

💾 Model Comparison:
-------------------
# Train multiple models and compare
python cifar10_agent.py train --architecture basic --epochs 30
python cifar10_agent.py train --architecture advanced --epochs 30

# Then evaluate both
python cifar10_agent.py evaluate --model-path checkpoints/cifar10_basic_*.h5
python cifar10_agent.py evaluate --model-path checkpoints/cifar10_advanced_*.h5

🚀 PRO TIPS
===========

1. Monitor with TensorBoard:
   # After training, view logs
   tensorboard --logdir logs/

2. Check training plots:
   - Look in plots/ directory for automatic training visualizations

3. Compare models:
   - Each training run creates timestamped checkpoints
   - Use different architectures and compare results

4. GPU vs CPU:
   - If you have a GPU, training will be much faster
   - Mixed precision (--mixed-precision) gives ~2x speedup on modern GPUs

🎯 CIFAR-10 CLASSES REMINDER
============================
0. airplane ✈️
1. automobile 🚗
2. bird 🐦
3. cat 🐱
4. deer 🦌
5. dog 🐕
6. frog 🐸
7. horse 🐎
8. ship 🚢
9. truck 🚛

🔥 CHALLENGE YOURSELF
====================

Try to beat these benchmarks:
- Basic Model: Can you get >80% accuracy?
- Advanced Model: Can you get >90% accuracy?
- Speed: Can you train to 85% accuracy in under 1 hour?

📝 EXPERIMENT LOG
=================

Keep track of your experiments here:

Date: ___________
Command: _________________________________________________
Result: __________________________________________________
Notes: ___________________________________________________

Date: ___________
Command: _________________________________________________
Result: __________________________________________________
Notes: ___________________________________________________

Date: ___________
Command: _________________________________________________
Result: __________________________________________________
Notes: ___________________________________________________

Happy Training! 🎉

For more help: python cifar10_agent.py --help
