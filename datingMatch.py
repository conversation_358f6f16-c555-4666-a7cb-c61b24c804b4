import pymongo

MONGODB_URI = "mongodb+srv://jschmitz:<EMAIL>/?retryWrites=true&w=majority"
DB_NAME = "DateFinder"
COLLECTION_NAME = "Singles"

mongo_client = pymongo.MongoClient(MONGODB_URI)
collection = mongo_client[DB_NAME][COLLECTION_NAME]
#Generate Embeddings with OpenAI
import openai

openai.api_key = "***********************************************************************************************"

def get_embedding(text):
    response = openai.Embedding.create(input=text, model="text-embedding-3-small")
    return response['data'][0]['embedding']
#Embed All Profiles
for doc in collection.find({"profileEmbedding": {"$exists": False}}):
    embedding = get_embedding(doc["bio"])
    collection.update_one({"_id": doc["_id"]}, {"$set": {"profileEmbedding": embedding}})
#Find Matches Using Atlas Vector Search
query_user = collection.find_one({"user_id": "u001"})
query_embedding = query_user["profileEmbedding"]
target_gender = "female"
location = query_user["location"]

pipeline = [
    {
        "$vectorSearch": {
            "index": "match_index",
            "path": "profileEmbedding",
            "queryVector": query_embedding,
            "numCandidates": 100,
            "limit": 3,
            "filter": {
                "gender": target_gender,
                "location": location
            }
        }
    },
    {
        "$project": {
            "name": 1,
            "gender": 1,
            "location": 1,
            "bio": 1,
            "score": {"$meta": "vectorSearchScore"}
        }
    }
]
matches = list(collection.aggregate(pipeline))
for match in matches:
    print(f"- {match['name']} ({match['gender']}), {match['location']}")
    print(f"  📖 {match['bio']}")
    print(f"  🔢 Score: {round(match['score'], 3)}\n")
